AWSTemplateFormatVersion: '2010-09-09'
Description: 'FluxTrader AWS Infrastructure - EC2, RDS, Secrets Manager, and Load Balancer'

Parameters:
  Environment:
    Type: String
    Default: staging
    AllowedValues: [staging, production]
    Description: Environment name (staging or production)
    
  InstanceType:
    Type: String
    Default: t3.medium
    AllowedValues: [t3.micro, t3.small, t3.medium, t3.large, t3.xlarge]
    Description: EC2 instance type
    
  RDSInstanceClass:
    Type: String
    Default: db.t3.micro
    AllowedValues: [db.t3.micro, db.t3.small, db.t3.medium, db.t3.large]
    Description: RDS instance class
    
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access
    
  DBMasterPassword:
    Type: String
    NoEcho: true
    MinLength: 8
    MaxLength: 128
    Description: Master password for RDS instance
    
  AllowedCIDR:
    Type: String
    Default: 0.0.0.0/0
    Description: CIDR block allowed to access the application
    
Conditions:
  IsProduction: !Equals [!Ref Environment, production]
  IsStaging: !Equals [!Ref Environment, staging]

Resources:
  # VPC and Networking (using default VPC for simplicity)
  FluxTraderSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub 'fluxtrader-${Environment}-sg'
      GroupDescription: !Sub 'Security group for FluxTrader ${Environment} environment'
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: !Ref AllowedCIDR
          Description: SSH access
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
          Description: HTTP access
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS access
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          SourceSecurityGroupId: !Ref FluxTraderSecurityGroup
          Description: Application port (internal)
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-sg'
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: fluxtrader

  # RDS Subnet Group
  FluxTraderDBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupName: !Sub 'fluxtrader-${Environment}-db-subnet-group'
      DBSubnetGroupDescription: !Sub 'Subnet group for FluxTrader ${Environment} RDS'
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-db-subnet-group'
        - Key: Environment
          Value: !Ref Environment

  # Private Subnets for RDS
  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref AWS::NoValue  # Use default VPC
      CidrBlock: ********/24
      AvailabilityZone: !Select [0, !GetAZs '']
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-private-subnet-1'

  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref AWS::NoValue  # Use default VPC
      CidrBlock: ********/24
      AvailabilityZone: !Select [1, !GetAZs '']
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-private-subnet-2'

  # RDS Instance
  FluxTraderDatabase:
    Type: AWS::RDS::DBInstance
    DeletionPolicy: !If [IsProduction, Snapshot, Delete]
    Properties:
      DBInstanceIdentifier: !Sub 'fluxtrader-${Environment}'
      DBInstanceClass: !Ref RDSInstanceClass
      Engine: postgres
      EngineVersion: '15.4'
      MasterUsername: fluxtrader
      MasterUserPassword: !Ref DBMasterPassword
      AllocatedStorage: !If [IsProduction, 100, 20]
      MaxAllocatedStorage: !If [IsProduction, 1000, 100]
      StorageType: gp3
      StorageEncrypted: true
      MultiAZ: !If [IsProduction, true, false]
      BackupRetentionPeriod: !If [IsProduction, 30, 7]
      DeleteAutomatedBackups: !If [IsProduction, false, true]
      DeletionProtection: !If [IsProduction, true, false]
      DBName: kamikaze
      VPCSecurityGroups:
        - !Ref FluxTraderSecurityGroup
      DBSubnetGroupName: !Ref FluxTraderDBSubnetGroup
      EnablePerformanceInsights: !If [IsProduction, true, false]
      MonitoringInterval: !If [IsProduction, 60, 0]
      MonitoringRoleArn: !If 
        - IsProduction
        - !GetAtt RDSEnhancedMonitoringRole.Arn
        - !Ref AWS::NoValue
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-db'
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: fluxtrader

  # RDS Enhanced Monitoring Role (Production only)
  RDSEnhancedMonitoringRole:
    Type: AWS::IAM::Role
    Condition: IsProduction
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: monitoring.rds.amazonaws.com
            Action: 'sts:AssumeRole'
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole'
      Path: /

  # IAM Role for EC2 Instance
  FluxTraderEC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'fluxtrader-${Environment}-ec2-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
      Policies:
        - PolicyName: FluxTraderSecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:fluxtrader/${Environment}/*'
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                Resource: !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:*'

  # Instance Profile
  FluxTraderInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: !Sub 'fluxtrader-${Environment}-instance-profile'
      Roles:
        - !Ref FluxTraderEC2Role

  # Launch Template
  FluxTraderLaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Properties:
      LaunchTemplateName: !Sub 'fluxtrader-${Environment}-launch-template'
      LaunchTemplateData:
        ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2023
        InstanceType: !Ref InstanceType
        KeyName: !Ref KeyPairName
        IamInstanceProfile:
          Arn: !GetAtt FluxTraderInstanceProfile.Arn
        SecurityGroupIds:
          - !Ref FluxTraderSecurityGroup
        UserData:
          Fn::Base64: !Sub |
            #!/bin/bash
            yum update -y
            yum install -y docker
            systemctl start docker
            systemctl enable docker
            usermod -a -G docker ec2-user
            
            # Install AWS CLI v2
            curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            unzip awscliv2.zip
            ./aws/install
            
            # Install CloudWatch agent
            yum install -y amazon-cloudwatch-agent
            
            # Create application directory
            mkdir -p /opt/fluxtrader
            chown ec2-user:ec2-user /opt/fluxtrader
            
            # Set environment variables
            echo "ENVIRONMENT=${Environment}" >> /etc/environment
            echo "AWS_DEFAULT_REGION=${AWS::Region}" >> /etc/environment
            
        TagSpecifications:
          - ResourceType: instance
            Tags:
              - Key: Name
                Value: !Sub 'fluxtrader-${Environment}'
              - Key: Environment
                Value: !Ref Environment
              - Key: Application
                Value: fluxtrader

  # EC2 Instance
  FluxTraderInstance:
    Type: AWS::EC2::Instance
    Properties:
      LaunchTemplate:
        LaunchTemplateId: !Ref FluxTraderLaunchTemplate
        Version: !GetAtt FluxTraderLaunchTemplate.LatestVersionNumber
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: fluxtrader

  # Application Load Balancer (Production only)
  FluxTraderALB:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Condition: IsProduction
    Properties:
      Name: !Sub 'fluxtrader-${Environment}-alb'
      Type: application
      Scheme: internet-facing
      SecurityGroups:
        - !Ref FluxTraderSecurityGroup
      Subnets:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
      Tags:
        - Key: Name
          Value: !Sub 'fluxtrader-${Environment}-alb'
        - Key: Environment
          Value: !Ref Environment

  # Target Group (Production only)
  FluxTraderTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Condition: IsProduction
    Properties:
      Name: !Sub 'fluxtrader-${Environment}-tg'
      Port: 8000
      Protocol: HTTP
      VpcId: !Ref AWS::NoValue  # Use default VPC
      HealthCheckPath: /health
      HealthCheckProtocol: HTTP
      HealthCheckIntervalSeconds: 30
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 5
      Targets:
        - Id: !Ref FluxTraderInstance
          Port: 8000

  # ALB Listener (Production only)
  FluxTraderALBListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Condition: IsProduction
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref FluxTraderTargetGroup
      LoadBalancerArn: !Ref FluxTraderALB
      Port: 80
      Protocol: HTTP

Outputs:
  InstanceId:
    Description: EC2 Instance ID
    Value: !Ref FluxTraderInstance
    Export:
      Name: !Sub '${AWS::StackName}-InstanceId'

  InstancePublicIP:
    Description: Public IP address of the EC2 instance
    Value: !GetAtt FluxTraderInstance.PublicIp
    Export:
      Name: !Sub '${AWS::StackName}-InstancePublicIP'

  DatabaseEndpoint:
    Description: RDS Database Endpoint
    Value: !GetAtt FluxTraderDatabase.Endpoint.Address
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseEndpoint'

  DatabasePort:
    Description: RDS Database Port
    Value: !GetAtt FluxTraderDatabase.Endpoint.Port
    Export:
      Name: !Sub '${AWS::StackName}-DatabasePort'

  LoadBalancerDNS:
    Condition: IsProduction
    Description: Application Load Balancer DNS Name
    Value: !GetAtt FluxTraderALB.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-LoadBalancerDNS'

  SecurityGroupId:
    Description: Security Group ID
    Value: !Ref FluxTraderSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-SecurityGroupId'
