# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
docs/
*.md
README.md

# CI/CD
.github/
.gitlab-ci.yml

# Development files
tests/
*.test
test_*
*_test.py

# Logs
logs/
*.log

# Node modules (if any)
node_modules/

# Temporary files
tmp/
temp/
*.tmp
