# FluxTrader - Cleaned Requirements
# Only packages actually used in the codebase

# Core dependencies
aiohttp>=3.8.0
aiohttp-cors>=0.7.0
python-dotenv>=0.19.0
groq>=0.4.0

# MCP (Model Context Protocol) dependencies
fastmcp>=0.1.0
mcp>=1.0.0

# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0

# Technical Analysis
TA-Lib>=0.4.25

# Web interface and API
streamlit>=1.28.0
plotly>=5.17.0
fastapi>=0.104.0
uvicorn>=0.24.0

# HTTP requests and WebSocket support
requests>=2.28.0
websockets>=11.0.0
websocket-client>=1.8.0
certifi>=2023.7.22

# System monitoring and utilities
psutil>=5.9.0

# Database support
asyncpg>=0.29.0
psycopg2-binary>=2.9.0

# Authentication and security
PyJWT>=2.8.0
cryptography>=41.0.0

# AWS integration
boto3>=1.34.0
botocore>=1.34.0

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
